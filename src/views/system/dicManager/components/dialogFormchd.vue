<script setup lang="ts">
import { reactive, watch, ref } from 'vue'
import { get, post, put } from '@/utils/http/request'
import { ElMessage } from 'element-plus'
const emits = defineEmits(['ok'])
const props = defineProps({
  code: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: true
  }
})
const visible = defineModel<boolean>()
const rules = reactive({
  value: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入编码', trigger: 'blur' }]
})
const formData = ref<any>({
  pCode: props.code
})
watch(
  () => visible.value,
  () => {
    if (visible.value) {
      if (props.type === 'edit') {
        get('/build/dict/' + props.code).then(res => {
          console.log('获取到数据:', res)
          if (res && res.success) {
            formData.value = res.data
          }
        })
      }
    }
  }
)
const handleClose = () => {
  visible.value = false
}
const handleSubmit = () => {
  console.log('handleSubmit', formData.value, props.code, props.type)
  if (props.type === 'edit') {
    put('/build/dict/' + props.code, formData.value).then(res => {
      console.log('获取到数据:', res)
      if (res && res.success) {
        visible.value = false
        emits('ok')
      } else {
        ElMessage.error(res.msg)
      }
    })
    return
  }
  post('/build/dict', {
    pCode: props.code,
    ...formData.value
  }).then(res => {
    console.log('获取到数据:', res)
    if (res && res.success) {
      visible.value = false
      emits('ok')
    } else {
      ElMessage.error(res.msg)
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="props.type === 'add' ? '新增' : '编辑'"
    width="500px"
  >
    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      :rules="rules"
      class="check-form"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="名称" prop="value">
            <el-input v-model="formData.value" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="编码" prop="code">
            <el-input v-model="formData.code" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit"> 提交 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.option-list {
  margin-top: 8px;
  margin-left: 100px;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.option-label {
  width: 60px;
  padding: 2px 4px;
  margin-right: 6px;
  font-weight: bold;
  text-align: right;
  background: #eee;
  border-radius: 2px;
}
</style>
