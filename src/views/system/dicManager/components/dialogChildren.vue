<template>
  <div class="mb-2 flex justify-items-start">
    <el-button type="primary" @click="handleAdd"> 新增 </el-button>
  </div>
  <div>
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      max-height="500"
    >
      <el-table-column label="字典名称" prop="value" />
      <el-table-column label="字典编号" prop="code" />
      <el-table-column label="备注" prop="remark" />
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            link
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-popconfirm
            class="box-item"
            title="确认删除该资源吗"
            placement="top-start"
            @confirm="handleDelete(scope.row)"
          >
            <template #reference>
              <el-button size="small" type="danger" link> 删除 </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <dialogForm
    ref="formRef"
    v-model="visible"
    :formData="crrentData"
    :type="ctype"
    @ok="getList"
  />
</template>
<script lang="ts" setup>
import { get, post } from '@/utils/http/request'
import { ref } from 'vue'
import dialogForm from '@v/system/dicManager/components/dialogFormchd.vue'
const props = defineProps({
  code: {
    type: String,
    required: true
  }
})
const tableData = ref([])
const loading = ref(false)
const visible = ref(false)
const ctype = ref('add')
const crrentData = ref({})
const getList = () => {
  console.log('getList', props.code)
  loading.value = true
  get('/build/dict/p/' + props.code).then(res => {
    console.log('获取到数据:', res)
    loading.value = false
    if (res && res.success) {
      tableData.value = res.data || []
    } else {
      tableData.value = []
    }
  })
}
getList()
const handleAdd = () => {
  visible.value = true
}
const handleEdit = row => {
  ctype.value = 'edit'
  visible.value = true
  crrentData.value = row
}
const handleDelete = row => {
  console.log('handleDelete', row)
}
</script>
